import { Theme<PERSON>rovider } from "@/components/theme-provider";
import type { Metadata } from "next";
import "./globals.css";
import { <PERSON><PERSON>rovider } from "@clerk/nextjs";
import { SessionProvider } from "@/components/providers/SessionProvider";
import { ClerkSessionProvider } from "@/components/providers/ClerkSessionProvider";
import { QueryProvider } from "@/components/providers/QueryProvider";
import { Toaster } from "@/components/ui/sonner";
import { BackgroundProvider } from "@/contexts/background-context";
import { inter } from "@/lib/fonts";
import { PHProvider } from "@/components/analytics/PostHogProvider";
import { generateSEOMetadata } from "@/components/seo/SEOHead";
import { StructuredData, structuredDataSchemas } from "@/components/seo/StructuredData";

export const metadata: Metadata = generateSEOMetadata({
  title: "Siift - Find your Founder Mode",
  description: "Your strategic co-founder for getting things done. Cut through the noise, drive real results, and build with confidence—all in one focused workspace.",
  keywords: ["founder tools", "startup productivity", "strategic planning", "execution", "founder mode", "startup growth", "focus tools"],
  url: "/",
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Organization structured data
  const organizationData = structuredDataSchemas.organization({
    name: "Siift",
    url: "https://siift.app",
    logo: "https://siift.app/images/logo.png",
    description: "Your strategic co-founder for getting things done. Less noise, more results.",
    socialMedia: [
      "https://twitter.com/siiftapp",
      "https://linkedin.com/company/siift"
    ],
  });

  // Website structured data
  const websiteData = structuredDataSchemas.website({
    name: "Siift",
    url: "https://siift.app",
    description: "Find your Founder Mode. Focus on the highest-impact moves and ship with confidence.",
    searchUrl: "https://siift.app/search?q={search_term_string}",
  });

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Structured Data */}
        <StructuredData data={[organizationData, websiteData]} />
      </head>
      <body
        className={`${inter.variable} antialiased font-sans`}
        suppressHydrationWarning
      >
        <ClerkProvider>
          <QueryProvider>
            <PHProvider>
              <ThemeProvider
                attribute="class"
                defaultTheme="system"
                enableSystem
                disableTransitionOnChange
              >
                <BackgroundProvider>
                  <SessionProvider>
                    <ClerkSessionProvider>
                      {children}
                      <Toaster />
                    </ClerkSessionProvider>
                  </SessionProvider>
                </BackgroundProvider>
              </ThemeProvider>
            </PHProvider>
          </QueryProvider>
        </ClerkProvider>
      </body>
    </html>
  );
}
