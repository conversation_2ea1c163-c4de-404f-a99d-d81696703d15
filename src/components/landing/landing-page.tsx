"use client";

import { Footer } from "@/components/layout/footer";
import { DottedBackground } from "@/components/ui/dotted-background";
import { WaitlistSection } from "@/components/ui/waitlist-section";
import { HeroSection } from "./hero-section";

export function LandingPage() {
  return (
    <div className="min-h-screen flex flex-col bg-background relative overflow-hidden">
      {/* Dotted Background Pattern */}
      <DottedBackground
        fadeEdge={90}
        dotSizes={[1, 1.5, 2]}
        spacing={25}
        dotsPerRow={6}
        opacity={0.2}
        darkOpacity={0.3}
        lightColors={["CCCCCC", "BBBBBB", "DDDDDD"]}
        darkColors={["666666", "777777", "555555"]}
      />

      <main className="flex-1 relative z-10">
        <HeroSection />
        <WaitlistSection />
      </main>
      <Footer />
    </div>
  );
}
