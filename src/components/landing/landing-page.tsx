"use client";

import { Footer } from "@/components/layout/footer";
import { DottedBackground } from "@/components/ui/dotted-background";
import { WaitlistSection } from "@/components/ui/waitlist-section";
import { HeroSection } from "./hero-section";

export function LandingPage() {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <main className="flex-1">
        <HeroSection />
        {/* Waitlist Section with Dotted Background */}
        <div className="relative overflow-hidden">
          <div className="absolute inset-0">
            <DottedBackground
              fadeEdge={90}
              dotSizes={[1, 1.5, 2]}
              spacing={25}
              dotsPerRow={8}
              opacity={0.2}
              darkOpacity={0.3}
              lightColors={["CCCCCC", "BBBBBB", "DDDDDD"]}
              darkColors={["666666", "777777", "555555"]}
              className="bg-center"
            />
          </div>
          <div className="relative z-10 w-full">
            <WaitlistSection />
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
