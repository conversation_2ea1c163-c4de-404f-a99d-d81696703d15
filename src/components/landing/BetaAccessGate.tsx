"use client";

import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

const EXPECTED_SHA256_HEX =
  "f4340be4842ad8dc1b863a3386dcf0015abc063f60c76e495421407768a7e31f";

function bytesToHexString(bytes: ArrayBuffer): string {
  const byteArray = new Uint8Array(bytes);
  const hexCodes: string[] = [];
  for (let i = 0; i < byteArray.length; i += 1) {
    const hexCode = byteArray[i].toString(16).padStart(2, "0");
    hexCodes.push(hexCode);
  }
  return hexCodes.join("");
}

async function sha256Hex(input: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(input);
  const hashBuffer = await crypto.subtle.digest("SHA-256", data);
  return bytesToHexString(hashBuffer);
}

export function BetaAccessGate() {
  const router = useRouter();
  const [password, setPassword] = useState<string>("");
  const [isVerifying, setIsVerifying] = useState<boolean>(false);

  const canSubmit = useMemo(() => password.trim().length > 0 && !isVerifying, [
    password,
    isVerifying,
  ]);

  const setBetaCookieAndRedirect = useCallback(() => {
    const maxAgeDays = 7;
    const maxAgeSeconds = maxAgeDays * 24 * 60 * 60;
    const cookie = `beta_access=1; Path=/; Max-Age=${maxAgeSeconds}; SameSite=Lax`;
    // Avoid always setting Secure so it also works on localhost during development
    document.cookie = cookie;
    router.push("/auth/login");
  }, [router]);

  const handleSubmit = useCallback(
    async (e?: React.FormEvent) => {
      e?.preventDefault();
      if (!canSubmit) return;
      try {
        setIsVerifying(true);
        const hash = await sha256Hex(password.trim());
        if (hash === EXPECTED_SHA256_HEX) {
          setBetaCookieAndRedirect();
        } else {
          toast.error("Invalid access code. Please try again.");
        }
      } catch (err) {
        console.error("Beta access verification failed:", err);
        toast.error("Something went wrong. Please try again.");
      } finally {
        setIsVerifying(false);
      }
    },
    [canSubmit, password, setBetaCookieAndRedirect]
  );

  return (
    <form
      onSubmit={handleSubmit}
      className="mx-auto max-w-xl bg-background/80 backdrop-blur border rounded-2xl p-4 sm:p-6 shadow-xl"
    >
      <div className="text-left sm:text-center space-y-2 mb-4">
        <h2 className="text-xl sm:text-2xl font-semibold">Beta access required</h2>
        <p className="text-sm text-muted-foreground">
          Enter the beta access code to continue to sign in.
        </p>
      </div>

      <div className="flex flex-col sm:flex-row gap-3">
        <Input
          type="password"
          autoComplete="off"
          placeholder="Enter access code"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="flex-1"
        />
        <Button type="submit" disabled={!canSubmit} className="whitespace-nowrap">
          {isVerifying ? "Verifying..." : "Unlock beta"}
        </Button>
      </div>

      <div className="mt-3 text-xs text-muted-foreground">
        Your access is stored for 7 days on this device.
      </div>
    </form>
  );
}

export default BetaAccessGate;


