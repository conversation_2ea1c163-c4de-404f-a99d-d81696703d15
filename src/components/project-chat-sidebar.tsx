"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { MessageCircle, Columns2, Columns3 } from "lucide-react";
import { useState, useEffect } from "react";
import { useSidebar } from "@/components/ui/sidebar";
import {
  ChatBubble,
  ChatBubbleMessage,
} from "./ui/chat-bubble";
import { ChatMessageList } from "./ui/chat-message-list";
import { Logo } from "./ui/logo";
import { ICON_SIZES } from "@/lib/constants";
import { AI_Prompt } from "./ui/animated-ai-input";
import { useChatStore } from "@/stores/chatStore";
import { safeLocalStorage } from "@/lib/storage";
import { useAiChat } from "@/hooks/useAiChat";
import { AiIntakeProgress } from "@/components/ai/AiIntakeProgress";
import { useAiIntake } from "@/hooks/useAiIntake";
import type { ChatMessage } from "@/types/Chat.types";

interface ProjectChatSidebarProps {
  projectId: string;
  isCollapsed?: boolean;
  onCollapseChange?: (collapsed: boolean) => void;
  embedded?: boolean; // New prop to indicate if it's embedded in sidebar
  chatWidth?: '45%' | '45%';
  setChatWidth?: (width: '45%' | '45%') => void;
  isChatCollapsed?: boolean;
  setIsChatCollapsed?: (collapsed: boolean) => void;
  selectedBusinessItem?: any;
}

// Default welcome message
const getWelcomeMessage = (): ChatMessage => ({
  id: 'welcome',
  user: "Siift AI",
  avatar: "",
  message: "👋 Welcome to your project! I'm here to help you manage tasks, answer questions, and provide insights. What would you like to work on?",
  timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
  isCurrentUser: false,
});

export function ProjectChatSidebar({
  projectId,
  isCollapsed: externalIsCollapsed,
  onCollapseChange,
  embedded = false,
  chatWidth: externalChatWidth,
  setChatWidth: externalSetChatWidth,
  isChatCollapsed: externalIsChatCollapsed,
  setIsChatCollapsed: externalSetIsChatCollapsed,
  selectedBusinessItem,
}: ProjectChatSidebarProps) {
  const [internalIsCollapsed, setInternalIsCollapsed] = useState(false);
  const [internalChatWidth, setInternalChatWidth] = useState<'45%' | '45%'>('45%');
  const { setOpen, state } = useSidebar();
  
  // Chat store integration
  const { 
    messages, 
    chatSession,
    setMessages, 
    setChatSession,
  } = useChatStore();

  // AI chat hook for streaming
  const { sendMessage, cancelMessage, isLoading, isStreaming } = useAiChat(projectId);
  const { stage } = useAiIntake();
  const [showIntakeProgress, setShowIntakeProgress] = useState(false);
  
  // Auto-show intake progress when stage is active
  useEffect(() => {
    const isIntakeActive = stage && !['idle', 'ready'].includes(stage);
    setShowIntakeProgress(isIntakeActive);
  }, [stage]);

  // Initialize messages on mount (do not fake session ID)
  useEffect(() => {
    if (!projectId) return;
    const savedMessages = safeLocalStorage.getJSON<ChatMessage[]>(`chat_messages_${projectId}`, []);
    if (savedMessages.length > 0) {
      setMessages(savedMessages);
    } else {
      setMessages([getWelcomeMessage()]);
    }
  }, [projectId, setMessages]);

  // Handle sending messages - now using the useAiChat hook
  const handleSendMessage = sendMessage;

  // Use external state if provided, otherwise use internal state
  const isCollapsed =
    externalIsCollapsed !== undefined
      ? externalIsCollapsed
      : internalIsCollapsed;
  const setIsCollapsed = onCollapseChange || setInternalIsCollapsed;

  // Chat is always expanded - no collapse functionality
  const isChatCollapsed = false;
  const setIsChatCollapsed = () => {}; // No-op function

  const chatWidth =
    externalChatWidth !== undefined
      ? externalChatWidth
      : internalChatWidth;
  const setChatWidth = externalSetChatWidth || setInternalChatWidth;



  const handleWidthToggle = () => {
    if (setChatWidth) {
      setChatWidth(chatWidth === '45%' ? '45%' : '45%');
    }
  };

  // Render embedded version for sidebar
  if (embedded) {
    // Don't show anything when sidebar is collapsed
    if (state === "collapsed") {
      return null;
    }

    // Always show expanded chat - no collapse functionality
    return (
      <div 
        className={`w-full h-full border-t border-border ${
          selectedBusinessItem ? 'bg-[var(--siift-lightest-accent)]/70 dark:bg-[color:var(--siift-dark-accent)]/20' : 'bg-[var(--siift-light-main)]/60 dark:bg-[color:var(--siift-darker)]/40'
        } backdrop-blur-sm flex flex-col transition-all duration-300`}
      >
        {/* AI Intake Progress - dev only */}
        {process.env.NODE_ENV === 'development' && (
          <div className="p-3 border-b border-border/50">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">AI Processing</span>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setShowIntakeProgress(!showIntakeProgress)}
                className="h-6 text-xs"
              >
                {showIntakeProgress ? 'Hide' : 'Show'}
              </Button>
            </div>
            {showIntakeProgress && <AiIntakeProgress projectId={projectId} />}
          </div>
        )}

        {/* Messages */}
        <div className="flex-1 overflow-hidden w-full min-h-0 relative">
          
          <ChatMessageList className="w-full h-full">
            {messages.map((msg) => (
              <ChatBubble
                key={msg.id}
                className="mt-2"
                variant={msg.isCurrentUser ? "sent" : "received"}
              >
                {!msg.isCurrentUser && (
                  <div className="h-8 w-8 shrink-0 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Logo size={20} />
                  </div>
                )}
                <ChatBubbleMessage
                  variant={msg.isCurrentUser ? "sent" : "received"}>
                  <div className="text-sm leading-relaxed whitespace-pre-line">{msg.message}</div>
                  {!msg.isCurrentUser && msg.cta?.type === 'refetch_topics' && (
                  <div className="mt-2">
                    <Button
                      size="sm"
                      className="h-7"
                      onClick={async () => {
                        // Aggressively invalidate and refetch topics and entries using our shared client
                        const { queryClient } = await import('@/lib/queryClient');
                        const keys: any[] = [
                          ["all-project-topics-v2", projectId],
                          ["all-topic-entries", projectId],
                          ["topics", projectId],
                          ["business-sections", projectId]
                        ];
                        keys.forEach((k) => queryClient.invalidateQueries({ queryKey: k }));
                        keys.forEach((k) => queryClient.refetchQueries({ queryKey: k }));
                      }}
                    >
                      {msg.cta.label || 'Start Siifting'}
                    </Button>
                  </div>
                  )}
                </ChatBubbleMessage>
              </ChatBubble>
            ))}
          </ChatMessageList>
        </div>

        {/* Message Input */}
        <div className="p-3 border-t border-border/50 bg-muted/30 backdrop-blur-sm w-full flex-shrink-0">
          <AI_Prompt 
            onSendMessage={handleSendMessage}
            onStop={() => cancelMessage()}
            isLoading={isStreaming}
            placeholder={isStreaming ? "AI is responding..." : "Ask me anything about your project..."}
          />
          
          {/* Disclaimer */}
          <div className="mt-1 px-2">
            <p className="text-[10px] text-center opacity-50 leading-tight">
              Siift can make mistakes. Please double check answers to ensure accuracy.
            </p>
          </div>
        </div>
      </div>
    );
  }
}
