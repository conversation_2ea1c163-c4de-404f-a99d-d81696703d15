"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, ArrowRight, MoreHorizontal } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useProjects } from "@/hooks/queries/useProjects";

// Updated: Removed add new project button, removed progress, reduced padding, added hover effects

export function DashboardProjectsSection() {
  const router = useRouter();
  const { data, isLoading, error } = useProjects();
  const projects = Array.isArray(data) ? data : [];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
      case "in-progress":
      return "bg-[var(--siift-light-main)] text-[var(--siift-darkest)] dark:bg-[color:var(--siift-darker)]/40 dark:text-[var(--siift-light-main)]";
      case "completed":
      return "bg-[var(--siift-light-main)] text-[var(--siift-darkest)] dark:bg-[color:var(--siift-darker)]/40 dark:text-[var(--siift-light-main)]";
      case "planning":
      return "bg-[var(--siift-light-mid)] text-[var(--siift-darkest)] dark:bg-[color:var(--siift-darker)]/40 dark:text-[var(--siift-light-main)]";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";
    }
  };

  const getProgressColor = (color: string) => {
    switch (color) {
      case "blue":
      return "bg-[var(--siift-mid-accent)]";
      case "purple":
      return "bg-[var(--siift-dark-accent)]";
      case "green":
        return "bg-green-600";
      case "indigo":
      return "bg-[var(--siift-dark-main)]";
      case "red":
        return "bg-red-600";
      default:
        return "bg-gray-600";
    }
  };

  const getAvatarColor = (color: string) => {
    switch (color) {
      case "blue":
      return "bg-[var(--siift-light-main)] dark:bg-[color:var(--siift-darker)] text-[var(--siift-mid-accent)] dark:text-[var(--siift-light-main)]";
      case "purple":
      return "bg-[var(--siift-lightest-accent)] dark:bg-[color:var(--siift-dark-accent)]/40 text-[var(--siift-dark-accent)] dark:text-[var(--siift-lightest)]";
      case "green":
        return "bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400";
      case "indigo":
      return "bg-[var(--siift-light-mid)] dark:bg-[color:var(--siift-darker)] text-[var(--siift-dark-main)] dark:text-[var(--siift-light-main)]";
      case "red":
        return "bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400";
      default:
        return "bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400";
    }
  };

  const handleViewAllProjects = () => {
    router.push("/projects");
  };

  return (
    <div className="space-y-6">
      <Card className="bg-gray-50 dark:bg-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                Recent Projects
              </CardTitle>
              <CardDescription>
                Your active projects and quick access to create new ones.
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleViewAllProjects}
              className="border-2 hover:bg-primary/10 hover:text-primary transition-all duration-200"
            >
              <ArrowRight className="mr-2 h-4 w-4" />
              View All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading && (
            <div className="text-sm text-muted-foreground">Loading projects...</div>
          )}
          {error && (
            <div className="text-sm text-destructive">Failed to load projects</div>
          )}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {/* Project Cards */}
            {projects.slice(0, 6).map((project: any) => (
              <Card
                key={project.id}
                className="bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer group"
                onClick={() => router.push(`/projects/${project.id}`)}
              >
                <CardContent className="px-4 py-0">
                  <div className="flex items-start justify-between mb-3">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getAvatarColor("blue")}`}></div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className={getStatusColor(project.status)}>
                        {project.status.replace("-", " ")}
                      </Badge>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <h3 className="font-medium mb-2">{project.name}</h3>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {project.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
